import { useCallback } from 'react';
import { SetupState } from '@src/types/setup';

interface SetupActions {
  setPhase: (phase: SetupState['currentPhase']) => void;
  setError: (error: string, type?: 'download' | 'backend' | 'mainApp') => void;
  setDownloadState: (downloading: boolean, progress?: any) => void;
  setDownloadComplete: () => void;
  setBackendState: (downloading: boolean, progress?: any) => void;
  setBackendComplete: () => void;
  setLaunchReady: (readiness: any) => void;
}

/**
 * Hook for setup-related IPC actions with proper error handling
 */
export const useSetupActions = (actions: SetupActions) => {
  // Helper for IPC calls with error handling
  const safeIpcCall = useCallback(async <T>(
    operation: () => Promise<T>,
    errorType: 'download' | 'backend' | 'mainApp' = 'download'
  ): Promise<T | null> => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
      return await operation();
    } catch (error) {
      console.error(`IPC call failed:`, error);
      actions.setError(
        error instanceof Error ? error.message : String(error),
        errorType
      );
      return null;
    }
  }, [actions]);

  // Check launch readiness
  const checkLaunchReady = useCallback(async () => {
    actions.setPhase('checking');
    
    const result = await safeIpcCall(
      () => window.electron!.launcher.checkReady()
    );

    if (result) {
      actions.setLaunchReady(result);
      return !result.allReady;
    }
    return false;
  }, [actions, safeIpcCall]);

  // Start main app download
  const startMainAppDownload = useCallback(async () => {
    actions.setDownloadState(true);
    actions.setPhase('downloading');

    const result = await safeIpcCall(
      () => window.electron!.download.start()
    );

    if (!result) {
      console.log('[SETUP] Main app download already in progress or completed');
      return true;
    }

    return true;
  }, [actions, safeIpcCall]);

  // Start backend download
  const startBackendDownload = useCallback(async () => {
    const result = await safeIpcCall(
      () => window.electron!.backend.download(),
      'backend'
    );

    if (!result) {
      console.log('[SETUP] Backend download already in progress or completed');
    }
  }, [actions, safeIpcCall]);

  // Start Microsoft Store setup
  const startMicrosoftStoreSetup = useCallback(async () => {
    actions.setDownloadState(true);
    actions.setPhase('downloading');

    console.log('[SETUP] Starting Microsoft Store MainApp copy...');

    const result = await safeIpcCall(
      () => window.electron!.download.start()
    );

    if (result) {
      console.log('[SETUP] Microsoft Store MainApp copy completed successfully');
      actions.setDownloadComplete();
      return true;
    }
    return false;
  }, [actions, safeIpcCall]);

  // Cancel downloads
  const cancelMainAppDownload = useCallback(async () => {
    const result = await safeIpcCall(
      () => window.electron!.download.cancel()
    );

    if (result) {
      actions.setDownloadState(false);
    }
    return result || false;
  }, [actions, safeIpcCall]);

  const cancelBackendDownload = useCallback(async () => {
    await safeIpcCall(
      () => window.electron!.backend.cancel(),
      'backend'
    );
    actions.setBackendState(false);
  }, [actions, safeIpcCall]);

  // Launch main app
  const launchMainApp = useCallback(async () => {
    actions.setPhase('starting');

    const result = await safeIpcCall(
      () => window.electron!.launcher.launchMainApp(),
      'mainApp'
    );

    if (result) {
      actions.setPhase('waiting');
      return true;
    }
    return false;
  }, [actions, safeIpcCall]);

  // Exit launcher
  const exitLauncher = useCallback(async () => {
    await safeIpcCall(
      () => window.electron!.launcher.exit()
    );
  }, [safeIpcCall]);

  return {
    checkLaunchReady,
    startMainAppDownload,
    startBackendDownload,
    startMicrosoftStoreSetup,
    cancelMainAppDownload,
    cancelBackendDownload,
    launchMainApp,
    exitLauncher
  };
};
