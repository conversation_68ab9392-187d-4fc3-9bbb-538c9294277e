{"packages": {"accelerate": {"version": "==0.34.0", "source": "pypi", "category": "ml"}, "aiofiles": {"version": "==24.1.0", "source": "pypi", "category": "network"}, "aiohttp": {"version": "==3.10.10", "source": "pypi", "category": "network"}, "craft-text-detection": {"version": "", "source": "craft_git", "category": "cv", "description": "Modern CRAFT text detection with FP16 support and better GPU compatibility"}, "jellyfish": {"version": "==1.1.0", "source": "pypi", "category": "text"}, "keyboard": {"version": "==0.13.5", "source": "pypi", "category": "core"}, "logging-formatter-anticrlf": {"version": "==1.2.1", "source": "pypi", "category": "dev"}, "mss": {"version": "==9.0.1", "source": "pypi", "category": "system"}, "numpy": {"version": "==1.26.4", "source": "pypi", "category": "core", "description": "Using stable numpy 1.x for compatibility"}, "opencv-python-headless": {"version": "==*********", "source": "pypi", "category": "cv", "description": "Headless OpenCV for server environments, compatible with Python 3.11"}, "optimum": {"version": "==1.23.0", "source": "pypi", "category": "ml"}, "pillow": {"version": "==10.4.0", "source": "pypi", "category": "cv"}, "protobuf": {"version": "==4.25.5", "source": "pypi", "category": "core", "description": "Compatible with transformers"}, "pyannote-audio": {"version": "==3.3.1", "source": "pypi", "category": "ml"}, "PyAudioWPatch": {"version": "==********", "source": "pypi", "category": "audio"}, "pydub": {"version": "==0.25.1", "source": "pypi", "category": "audio"}, "pyinstaller": {"version": "==6.10.0", "source": "pypi", "category": "build"}, "pynvml": {"version": "==11.5.3", "source": "pypi", "category": "system"}, "pyperclip": {"version": "==1.9.0", "source": "pypi", "category": "core"}, "pywin32": {"version": "==306", "source": "pypi", "category": "system", "platform": "win32"}, "rich": {"version": "==13.8.1", "source": "pypi", "category": "dev"}, "scipy": {"version": "==1.11.4", "source": "pypi", "category": "core", "description": "Compatible with numpy 1.26.4"}, "setuptools": {"version": "==69.5.1", "source": "pypi", "category": "core", "description": "Known stable version < 81 that works well"}, "silero-vad": {"version": "==5.1.0", "source": "pypi", "category": "ml"}, "sounddevice": {"version": "==0.5.2", "source": "pypi", "category": "audio"}, "torch": {"version": "==2.4.1", "source": "torch_cuda", "category": "ml", "description": "PyTorch with CUDA support"}, "torchaudio": {"version": "==2.4.1", "source": "torch_cuda", "category": "ml"}, "torchvision": {"version": "==0.19.1", "source": "torch_cuda", "category": "ml"}, "transformers": {"version": "==4.44.2", "extras": ["torch"], "source": "pypi", "category": "ml", "description": "Compatible with optimum 1.23.0"}, "websockets": {"version": "==12.0", "source": "pypi", "category": "network"}}, "sources": {"pypi": {"type": "index", "url": "https://pypi.org/simple/"}, "torch_cuda": {"type": "index", "url": "https://download.pytorch.org/whl/cu121", "fallback_source": "pypi_cpu"}, "pypi_cpu": {"type": "index", "url": "https://pypi.org/simple/", "description": "Fallback for CPU-only torch packages"}, "craft_git": {"type": "git", "url": "https://github.com/ijazsadiqbasha/CRAFT-text-detection.git", "description": "Modern CRAFT implementation with FP16 and better GPU support"}}, "remove": [], "settings": {"auto_remove_unlisted": false, "track_managed_packages": true, "state_file": ".dist/python/.package-state.json", "uv": {"enabled": true, "fallback_to_pip": true, "version": "0.5.11"}}}