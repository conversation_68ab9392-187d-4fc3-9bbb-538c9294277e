import { spawn, exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import ora from 'ora';
import sevenBin from '7zip-bin';

// =============================================================================
// CONFIGURATION CONSTANTS
// =============================================================================

// Python distribution configuration
export const REPO_INFO = {
  owner: 'indygreg',
  repo: 'python-build-standalone',
  tag: '20240107',
  version: 'cpython-3.11.7'
};

// UV package manager configuration for faster installations
export const UV_CONFIG = {
  version: '0.5.11',
  downloadUrls: {
    win32: {
      x64: 'https://github.com/astral-sh/uv/releases/download/0.5.11/uv-x86_64-pc-windows-msvc.zip'
    },
    darwin: {
      x64: 'https://github.com/astral-sh/uv/releases/download/0.5.11/uv-x86_64-apple-darwin.tar.gz',
      arm64: 'https://github.com/astral-sh/uv/releases/download/0.5.11/uv-aarch64-apple-darwin.tar.gz'
    },
    linux: {
      x64: 'https://github.com/astral-sh/uv/releases/download/0.5.11/uv-x86_64-unknown-linux-gnu.tar.gz'
    }
  }
};

// Platform-specific Python asset mappings
export const PLATFORM_ASSETS = {
  win32: {
    x64: `${REPO_INFO.version}+${REPO_INFO.tag}-x86_64-pc-windows-msvc-shared-install_only.tar.gz`,
    arm64: `${REPO_INFO.version}+${REPO_INFO.tag}-aarch64-pc-windows-msvc-shared-install_only.tar.gz`
  },
  darwin: {
    x64: `${REPO_INFO.version}+${REPO_INFO.tag}-x86_64-apple-darwin-install_only.tar.gz`,
    arm64: `${REPO_INFO.version}+${REPO_INFO.tag}-aarch64-apple-darwin-install_only.tar.gz`
  },
  linux: {
    x64: `${REPO_INFO.version}+${REPO_INFO.tag}-x86_64-unknown-linux-gnu-install_only.tar.gz`,
    arm64: `${REPO_INFO.version}+${REPO_INFO.tag}-aarch64-unknown-linux-gnu-install_only.tar.gz`
  }
};

// =============================================================================
// ERROR HANDLING
// =============================================================================

// Custom error class for better error handling
export class PythonProcessError extends Error {
  exitCode: number;
  stdout: string;
  stderr: string;

  constructor(message: string, exitCode: number, stdout: string, stderr: string) {
    super(message);
    this.name = 'PythonProcessError';
    this.exitCode = exitCode;
    this.stdout = stdout;
    this.stderr = stderr;
  }
}

// =============================================================================
// PATH UTILITIES
// =============================================================================

// Get paths in a cross-platform way
export function getPaths() {
  const projectRoot = process.cwd();
  const distDir = path.join(projectRoot, '.dist');
  const pythonDir = path.join(distDir, 'python');
  
  // Platform-specific paths
  const pythonExePath = process.platform === 'win32'
    ? path.join(pythonDir, 'python.exe')
    : path.join(pythonDir, 'bin', 'python3');
    
  const sitePackagesPath = process.platform === 'win32'
    ? path.join(pythonDir, 'Lib', 'site-packages')
    : path.join(pythonDir, 'lib', 'python3.11', 'site-packages');
  
  return {
    projectRoot,
    distDir,
    pythonDir,
    pythonExePath,
    sitePackagesPath,
    outputDir: path.join(distDir, 'python-exe'),
    buildDir: path.join(distDir, 'python-exe', 'build'),
    specFilePath: path.join(projectRoot, 'python', 'backend.fresh.spec'),
    finalOutputDir: path.join(distDir, 'One Whispr Backend')
  };
}

// =============================================================================
// COMMAND EXECUTION UTILITIES
// =============================================================================

// Helper function to run commands
export async function runCommand(command: string, args: string[], options: any = {}): Promise<string> {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, {
      stdio: 'pipe',
      shell: process.platform === 'win32', // Use shell on Windows
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    if (proc.stdout) {
      proc.stdout.on('data', (data) => {
        stdout += data.toString();
      });
    }
    
    if (proc.stderr) {
      proc.stderr.on('data', (data) => {
        stderr += data.toString();
      });
    }
    
    proc.on('error', (error) => {
      reject(new Error(`Failed to run command: ${error.message}`));
    });
    
    proc.on('close', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        reject(new PythonProcessError(`Command exited with code ${code}`, code || 1, stdout, stderr));
      }
    });
  });
}

// Run Python process with detailed output handling
export async function runPythonProcess(
  pythonExePath: string, 
  args: string[], 
  options: any = {}
): Promise<{stdout: string, stderr: string, exitCode: number}> {
  return new Promise((resolve) => {
    const proc = spawn(pythonExePath, args, {
      stdio: 'pipe',
      shell: process.platform === 'win32',
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    if (proc.stdout) {
      proc.stdout.on('data', (data) => {
        const text = data.toString();
        stdout += text;
        // Optionally print to console if callback is provided
        if (options.onStdout) {
          options.onStdout(text);
        }
      });
    }
    
    if (proc.stderr) {
      proc.stderr.on('data', (data) => {
        const text = data.toString();
        stderr += text;
        if (options.onStderr) {
          options.onStderr(text);
        }
      });
    }
    
    proc.on('error', (error) => {
      resolve({
        stdout,
        stderr: stderr + `\nProcess error: ${error.message}`,
        exitCode: 1
      });
    });
    
    proc.on('close', (code) => {
      resolve({
        stdout,
        stderr,
        exitCode: code || 0
      });
    });
  });
}

  // Note: Package management functions moved to BackendPackageManager

// =============================================================================
// UV PACKAGE MANAGER UTILITIES
// =============================================================================

// Download and setup UV for faster package management
export async function downloadUV(): Promise<string | null> {
  try {
    const platform = process.platform as 'win32' | 'darwin' | 'linux';
    const arch = process.arch as 'x64' | 'arm64';

    const platformConfig = UV_CONFIG.downloadUrls[platform];
    const downloadUrl = platformConfig?.[arch as keyof typeof platformConfig];
    if (!downloadUrl) {
      console.warn(`UV not available for platform: ${platform}-${arch}`);
      return null;
    }

    const { pythonDir } = getPaths();
    const uvDir = path.join(pythonDir, 'uv');
    const uvExePath = path.join(uvDir, platform === 'win32' ? 'uv.exe' : 'uv');

    // Check if UV is already installed and working
    if (fs.existsSync(uvExePath)) {
      try {
        await runCommand(uvExePath, ['--version']);
        return uvExePath;
      } catch (error) {
        // UV exists but not working, reinstall
        fs.rmSync(uvDir, { recursive: true, force: true });
      }
    }

    // Create UV directory
    if (!fs.existsSync(uvDir)) {
      fs.mkdirSync(uvDir, { recursive: true });
    }

    // Download UV
    const response = await fetch(downloadUrl);
    if (!response.ok) {
      throw new Error(`Failed to download UV: ${response.statusText}`);
    }

    const buffer = Buffer.from(await response.arrayBuffer());

    // Extract using 7zip (works for both ZIP and tar.gz)
    const tempArchivePath = path.join(uvDir, downloadUrl.endsWith('.zip') ? 'uv.zip' : 'uv.tar.gz');
    fs.writeFileSync(tempArchivePath, buffer);

    try {
      // Use 7zip to extract the archive
      await runCommand(sevenBin.path7za, [
        'x', // Extract with full paths
        tempArchivePath,
        `-o${uvDir}`, // Output directory
        '-y' // Yes to all prompts
      ]);

      // Clean up the archive file
      fs.unlinkSync(tempArchivePath);
    } catch (error) {
      // Clean up on failure
      if (fs.existsSync(tempArchivePath)) {
        fs.unlinkSync(tempArchivePath);
      }
      throw error;
    }

    // Verify installation
    if (!fs.existsSync(uvExePath)) {
      throw new Error(`UV executable not found after extraction: ${uvExePath}`);
    }

    // Test UV works
    await runCommand(uvExePath, ['--version']);

    return uvExePath;
  } catch (error) {
    console.warn('Failed to setup UV:', error);
    return null;
  }
}


// =============================================================================
// PYINSTALLER UTILITIES
// =============================================================================

// Check if PyInstaller is installed
export async function checkPyInstaller(pythonExePath: string): Promise<boolean> {
  try {
    await runCommand(pythonExePath, ['-m', 'PyInstaller', '--version']);
    return true;
  } catch (error) {
    return false;
  }
}

// =============================================================================
// FILE SYSTEM UTILITIES
// =============================================================================

// Helper function to recursively copy a folder
export function copyFolderRecursiveSync(source: string, destination: string) {
  // Check if source exists
  if (!fs.existsSync(source)) {
    throw new Error(`Source directory not found: ${source}`);
  }
  
  const stats = fs.statSync(source);
  const isDirectory = stats.isDirectory();
  
  if (isDirectory) {
    // Create destination folder if it doesn't exist
    const destFolder = path.join(destination, path.basename(source));
    if (!fs.existsSync(destFolder)) {
      fs.mkdirSync(destFolder, { recursive: true });
    }
    
    // Copy all files in the directory
    fs.readdirSync(source).forEach(childItemName => {
      copyFolderRecursiveSync(
        path.join(source, childItemName),
        destFolder
      );
    });
  } else {
    // Copy file
    fs.copyFileSync(source, path.join(destination, path.basename(source)));
  }
}

// Helper function to count files in a directory recursively
export function countFiles(dir: string): number {
  let count = 0;
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      count += countFiles(itemPath);
    } else {
      count++;
    }
  }
  
  return count;
}

// Preserve site-packages directory
export function preserveSitePackages(sitePackagesPath: string): string | null {
  const { distDir } = getPaths();
  
  if (fs.existsSync(sitePackagesPath)) {
    const tempDir = path.join(distDir, 'temp-site-packages');
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    fs.mkdirSync(tempDir, { recursive: true });
    fs.cpSync(sitePackagesPath, tempDir, { recursive: true });
    return tempDir;
  }
  
  return null;
}

// Restore site-packages from a temporary directory
export function restoreSitePackages(tempDir: string | null, sitePackagesPath: string): void {
  if (tempDir && fs.existsSync(tempDir)) {
    if (!fs.existsSync(sitePackagesPath)) {
      fs.mkdirSync(sitePackagesPath, { recursive: true });
    }
    fs.cpSync(tempDir, sitePackagesPath, { recursive: true });
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
}

// =============================================================================
// PROCESS MANAGEMENT UTILITIES
// =============================================================================

// Kill any running Python processes on Windows
export async function killPythonProcesses(spinner?: ora.Ora): Promise<void> {
  if (process.platform === 'win32') {
    if (spinner) spinner.text = '🔄 Ensuring no Python processes are running...';
    try {
      await new Promise<void>((resolve) => {
        exec('taskkill /f /im python.exe /im pythonw.exe /im python3.exe', () => {
          // Ignore errors - the process might not exist
          resolve();
        });
      });
      
      // Add a delay to ensure processes are fully terminated
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      // Ignore errors - the process might not exist
    }
  }
} 