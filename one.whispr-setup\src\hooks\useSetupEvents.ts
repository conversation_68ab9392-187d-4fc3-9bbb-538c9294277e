import { useEffect, useRef } from 'react';
import { SetupState, DownloadProgress, BackendDownloadProgress } from '@src/types/setup';

interface UseSetupEventsProps {
  setState: (updates: Partial<SetupState>) => void;
  launchMainApp: () => Promise<boolean>;
}

/**
 * Hook for handling IPC events from the main process
 */
export const useSetupEvents = ({ setState, launchMainApp }: UseSetupEventsProps) => {
  // Use refs to avoid dependency changes
  const setStateRef = useRef(setState);
  const launchMainAppRef = useRef(launchMainApp);
  
  // Update refs when props change
  setStateRef.current = setState;
  launchMainAppRef.current = launchMainApp;

  useEffect(() => {
    console.log('[SETUP] Setting up event listeners');
    
    if (!window.electron) {
      console.error('Electron API not available');
      return;
    }
    
    // Main app download progress
    const removeDownloadProgressListener = window.electron.events.onDownloadProgress(
      (data: DownloadProgress) => {
        setStateRef.current({
          downloadProgress: data,
          isDownloading: true
        });
      }
    );

    // Main app download complete
    const removeDownloadCompleteListener = window.electron.events.onDownloadComplete(
      (_data: any) => {
        setStateRef.current({
          downloadComplete: true,
          isDownloading: false
        });
      }
    );

    // Main app download error
    const removeDownloadErrorListener = window.electron.events.onDownloadError(
      (data: any) => {
        setStateRef.current({
          downloadError: data.message || 'Unknown download error',
          isDownloading: false,
          currentPhase: 'error'
        });
      }
    );

    // Backend download progress
    const removeBackendProgressListener = window.electron.events.onBackendProgress(
      (data: BackendDownloadProgress) => {
        console.log('[FRONTEND] Received backend progress:', data);
        setStateRef.current({
          backendProgress: data,
          backendDownloading: true,
          currentPhase: 'downloading' // Ensure we're in downloading phase
        });
      }
    );

    // Backend download complete
    const removeBackendCompleteListener = window.electron.events.onBackendComplete(
      async () => {
        console.log('[SETUP] Backend complete event received');
        setStateRef.current({
          backendDownloading: false,
          backendComplete: true,
          backendProgress: null,
          backendError: null, // Clear any previous backend errors
          currentPhase: 'starting' // Move to starting phase when backend is ready
        });

        // Launch main app immediately when backend is complete
        console.log('[SETUP] Backend complete - launching main app');
        await launchMainAppRef.current();
      }
    );

    // Backend download error
    const removeBackendErrorListener = window.electron.events.onBackendError(
      (data: { message: string }) => {
        setStateRef.current({
          backendDownloading: false,
          backendError: data.message,
          backendProgress: null
        });
      }
    );
    
    // Main app ready
    const removeMainAppReadyListener = window.electron.events.onMainAppReady(
      (_data: any) => {
        setStateRef.current({
          currentPhase: 'starting'
        });
      }
    );

    // Main app error
    const removeMainAppErrorListener = window.electron.events.onMainAppError(
      (data: any) => {
        setStateRef.current({
          mainAppError: data.error || 'Unknown main app error',
          currentPhase: 'error'
        });
      }
    );
    
    // Clean up listeners
    return () => {
      console.log('[SETUP] Cleaning up event listeners');
      removeDownloadProgressListener();
      removeDownloadCompleteListener();
      removeDownloadErrorListener();
      removeBackendProgressListener();
      removeBackendCompleteListener();
      removeBackendErrorListener();
      removeMainAppReadyListener();
      removeMainAppErrorListener();
    };
  }, []);
};
