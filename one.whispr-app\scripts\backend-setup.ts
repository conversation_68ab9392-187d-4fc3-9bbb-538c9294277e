import { mkdirSync, existsSync, readFileSync, writeFileSync } from 'fs';
import { pipeline } from 'stream/promises';
import { extract } from 'tar';
import { createGunzip } from 'zlib';
import { Readable } from 'stream';
import path from 'path';
import { Octokit } from '@octokit/rest';
import ora from 'ora';
import * as fs from 'fs';
import {
  getPaths,
  runCommand,
  REPO_INFO,
  PLATFORM_ASSETS,
  preserveSitePackages,
  restoreSitePackages
} from './backend-utils';
import { BackendPackageManager } from './backend-package-manager';

// Downloads standalone Python distribution
async function downloadPython() {
  const spinner = ora('Preparing to download Python...').start();
  const { pythonDir, pythonExePath } = getPaths();
  
  try {
    // Check if Python is already installed
    if (existsSync(pythonExePath)) {
      // Verify Python works by running a simple command
      try {
        await runCommand(pythonExePath, ['--version']);
        spinner.succeed('✅ Python is already installed and working');
        return;
      } catch (error) {
        spinner.warn('⚠️ Python installation found but not working properly. Reinstalling...');
        // Continue with installation
      }
    }
    
    // Create directory if it doesn't exist
    if (!existsSync(pythonDir)) {
      mkdirSync(pythonDir, { recursive: true });
    } else {
      // Clean up existing directory to avoid conflicts
      spinner.text = 'Cleaning up existing Python directory...';
      try {
        // Simplified site-packages preservation
        const sitePackagesPath = getPaths().sitePackagesPath;
        const tempSitePackagesDir = preserveSitePackages(sitePackagesPath);
        
        // Clean Python directory
        fs.rmSync(pythonDir, { recursive: true, force: true });
        fs.mkdirSync(pythonDir, { recursive: true });
        
        // Restore site-packages
        restoreSitePackages(tempSitePackagesDir, sitePackagesPath);
      } catch (error: any) {
        spinner.warn(`⚠️ Could not clean up Python directory: ${error.message}`);
        // Continue with installation
      }
    }
    
    const platform = process.platform as 'win32' | 'darwin' | 'linux';
    const arch = process.arch as 'x64' | 'arm64';
    
    const assetName = PLATFORM_ASSETS[platform]?.[arch];
    if (!assetName) {
      spinner.fail(`❌ Unsupported platform: ${platform}-${arch}`);
      throw new Error(`Unsupported platform: ${platform}-${arch}`);
    }

    spinner.text = `🔍 Downloading Python for ${platform}-${arch}...`;
    
    const octokit = new Octokit();
    
    // Get release by tag
    const { data: release } = await octokit.repos.getReleaseByTag({
      ...REPO_INFO,
      tag: REPO_INFO.tag
    });

    // Find matching asset
    const asset = release.assets.find(asset => asset.name === assetName);
    if (!asset) {
      spinner.fail(`❌ Could not find asset: ${assetName}`);
      throw new Error(`Could not find asset: ${assetName}`);
    }

    spinner.text = `📥 Downloading ${asset.name} (${Math.round(asset.size / 1024 / 1024)}MB)...`;

    // Download asset
    const response = await octokit.request({
      url: asset.browser_download_url,
      headers: {
        'accept': 'application/octet-stream'
      },
      responseType: 'arraybuffer'
    });

    spinner.text = '📦 Extracting Python...';
    // Create a readable stream from the buffer
    const bufferStream = Readable.from(Buffer.from(response.data));
    
    await pipeline(
      bufferStream,
      createGunzip(),
      extract({ cwd: pythonDir, strip: 1 }) as unknown as NodeJS.WritableStream
    );

    // Verify installation
    if (existsSync(pythonExePath)) {
      try {
        const version = await runCommand(pythonExePath, ['--version']);
        spinner.succeed(`✅ Python installation complete! ${version.trim()}`);
      } catch (error) {
        spinner.succeed('✅ Python installation complete!');
      }
    } else {
      spinner.fail(`❌ Python executable not found at ${pythonExePath}`);
      throw new Error(`Python executable not found at ${pythonExePath}`);
    }
  } catch (error: any) {
    spinner.fail(`❌ Python installation failed: ${error.message}`);
    throw error;
  }
}

// Installs Python dependencies using the new BackendPackageManager
async function installDependencies() {
  const spinner = ora('🔍 Initializing package manager...').start();
  const { pythonExePath, sitePackagesPath, projectRoot } = getPaths();
  
  try {
    // Ensure Python is installed
    if (!existsSync(pythonExePath)) {
      spinner.fail(`❌ Python not found at ${pythonExePath}. Please run setup first.`);
      throw new Error(`Python not found at ${pythonExePath}`);
    }
    
    // Ensure site-packages directory exists
    if (!existsSync(sitePackagesPath)) {
      fs.mkdirSync(sitePackagesPath, { recursive: true });
    }

    // Initialize the new package manager
    spinner.text = '📦 Loading package manifest...';
    const packageManager = new BackendPackageManager(pythonExePath, sitePackagesPath);

    // Check UV availability
    spinner.text = '⚡ Checking UV availability...';
    const uvAvailable = await packageManager.isUVAvailable();
    if (uvAvailable) {
      spinner.info('✅ UV is available for fast package installation');
    } else {
      spinner.info('ℹ️ UV not available, will use pip as fallback');
    }

    // Get installation plan
    spinner.text = '🔍 Analyzing package requirements...';
    const plan = await packageManager.getInstallationPlan();
    
    // Show summary
    const totalPackages = Object.keys(packageManager.getPackagesByCategory()).reduce((total, category) => {
      return total + packageManager.getPackagesByCategory()[category].length;
    }, 0);

    if (plan.already_managed.length > 0) {
      spinner.info(`✅ Already managed: ${plan.already_managed.length}/${totalPackages} packages`);
    }

    if (plan.remove.length > 0) {
      spinner.info(`🗑️ Will remove: ${plan.remove.length} packages`);
    }

    if (plan.install.length === 0 && plan.remove.length === 0) {
      spinner.succeed('✅ All packages are up to date');
    } else {
      // Sync packages (install + remove)
      spinner.text = `📦 Syncing ${plan.install.length} packages...`;
      
      // Stop spinner for better output visibility during installation
      spinner.stop();
      
      if (uvAvailable) {
        console.log('⚡ Using UV for fast package installation...');
      } else {
        console.log('📦 Using pip for package installation...');
      }

      const result = await packageManager.syncPackages();
      
      // Restart spinner
      spinner.start();
      
      if (result.installed > 0) {
        spinner.succeed(`✅ Installed ${result.installed} packages`);
      }
      
      if (result.removed > 0) {
        spinner.succeed(`🗑️ Removed ${result.removed} packages`);
      }
      
      spinner.succeed(`✅ Package sync complete: ${result.unchanged} unchanged, ${result.installed} installed, ${result.removed} removed`);
    }
    
    // Add the python directory to Python path
    spinner.text = '🔧 Setting up Python path...';
    const pthPath = path.join(sitePackagesPath, 'project.pth');
    const srcPath = path.join(projectRoot, 'python');
    
    // Only create the .pth file if it doesn't exist or has changed
    if (!existsSync(pthPath) || readFileSync(pthPath, 'utf8') !== srcPath) {
      writeFileSync(pthPath, srcPath);
    }
    
    // Show package categories summary
    const categories = packageManager.getPackagesByCategory();
    spinner.info(`📊 Package categories: ${Object.keys(categories).map(cat => `${cat} (${categories[cat].length})`).join(', ')}`);
    
    spinner.succeed('✅ Python environment setup complete!');
  } catch (error: any) {
    spinner.fail(`❌ Dependency installation failed: ${error.message}`);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Setting up Python environment...');

  try {
    await downloadPython();
    await installDependencies();
    console.log('✅ Setup completed successfully!');
  } catch (err) {
    console.error('❌ Setup failed:', err);
    process.exit(1);
  }
}

// Run main if this script is executed directly
if (require.main === module) {
  main();
}

export {
  downloadPython,
  installDependencies
};