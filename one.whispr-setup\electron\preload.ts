import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the specific API methods we want to expose
// This is much more secure than exposing the raw ipcRenderer
contextBridge.exposeInMainWorld('electron', {
  // Launcher operations
  launcher: {
    checkReady: () => ipcRenderer.invoke('launcher:check-ready'),
    launchMainApp: () => ipcRenderer.invoke('launcher:launch-main-app'),
    getEnv: () => ipcRenderer.invoke('launcher:get-env'),
    exit: () => ipcRenderer.invoke('launcher:exit'),
  },

  // Download operations  
  download: {
    start: () => ipcRenderer.invoke('download:start'),
    cancel: () => ipcRenderer.invoke('download:cancel'),
  },

  // Backend operations
  backend: {
    download: () => ipcRenderer.invoke('backend:download'),
    cancel: () => ipcRenderer.invoke('backend:cancel'),
  },

  // Event listeners
  events: {
    // Download events
    onDownloadProgress: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('download:progress', handler);
      return () => ipcRenderer.removeListener('download:progress', handler);
    },
    
    onDownloadComplete: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('download:complete', handler);
      return () => ipcRenderer.removeListener('download:complete', handler);
    },
    
    onDownloadError: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('download:error', handler);
      return () => ipcRenderer.removeListener('download:error', handler);
    },

    // Backend events
    onBackendProgress: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('backend:progress', handler);
      return () => ipcRenderer.removeListener('backend:progress', handler);
    },
    
    onBackendComplete: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('backend:complete', handler);
      return () => ipcRenderer.removeListener('backend:complete', handler);
    },
    
    onBackendError: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('backend:error', handler);
      return () => ipcRenderer.removeListener('backend:error', handler);
    },

    // Launcher events
    onMainAppReady: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('launcher:main-app-ready', handler);
      return () => ipcRenderer.removeListener('launcher:main-app-ready', handler);
    },
    
    onMainAppError: (callback: (data: any) => void) => {
      const handler = (_event: any, data: any) => callback(data);
      ipcRenderer.on('launcher:main-app-error', handler);
      return () => ipcRenderer.removeListener('launcher:main-app-error', handler);
    },
  }
});
