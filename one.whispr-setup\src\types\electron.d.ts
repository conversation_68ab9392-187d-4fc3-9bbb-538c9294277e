/**
 * TypeScript declarations for Electron API with custom secure methods
 */
interface ElectronAPI {
  // Launcher operations
  launcher: {
    /**
     * Check if everything is ready to launch the main app
     */
    checkReady: () => Promise<any>;
    
    /**
     * Launch the main One Whispr application
     */
    launchMainApp: () => Promise<any>;
    
    /**
     * Get environment variables from main process
     */
    getEnv: () => Promise<any>;
    
    /**
     * Exit the launcher application
     */
    exit: () => Promise<any>;
  };

  // Download operations
  download: {
    /**
     * Start downloading the main application
     */
    start: () => Promise<any>;
    
    /**
     * Cancel the main application download
     */
    cancel: () => Promise<any>;
  };

  // Backend operations
  backend: {
    /**
     * Start downloading the backend components
     */
    download: () => Promise<any>;
    
    /**
     * Cancel the backend download
     */
    cancel: () => Promise<any>;
  };

  // Event listeners
  events: {
    /**
     * Listen for main app download progress updates
     * @returns Function to remove the listener
     */
    onDownloadProgress: (callback: (data: any) => void) => () => void;
    
    /**
     * Listen for main app download completion
     * @returns Function to remove the listener
     */
    onDownloadComplete: (callback: (data: any) => void) => () => void;
    
    /**
     * Listen for main app download errors
     * @returns Function to remove the listener
     */
    onDownloadError: (callback: (data: any) => void) => () => void;
    
    /**
     * Listen for backend download progress updates
     * @returns Function to remove the listener
     */
    onBackendProgress: (callback: (data: any) => void) => () => void;
    
    /**
     * Listen for backend download completion
     * @returns Function to remove the listener
     */
    onBackendComplete: (callback: (data: any) => void) => () => void;
    
    /**
     * Listen for backend download errors
     * @returns Function to remove the listener
     */
    onBackendError: (callback: (data: any) => void) => () => void;
    
    /**
     * Listen for main app ready events
     * @returns Function to remove the listener
     */
    onMainAppReady: (callback: (data: any) => void) => () => void;
    
    /**
     * Listen for main app errors
     * @returns Function to remove the listener
     */
    onMainAppError: (callback: (data: any) => void) => () => void;
  };
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}

export {}; 