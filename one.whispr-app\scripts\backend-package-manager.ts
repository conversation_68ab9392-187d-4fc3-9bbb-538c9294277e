import path from 'path';
import fs from 'fs';
import { runCommand } from './backend-utils';

// Types for our package configuration
export interface PackageConfig {
  version: string;
  source: string;
  category: string;
  extras?: string[];
  platform?: string;
  description?: string;
}

export interface PackageSource {
  type: 'index' | 'git' | 'file';
  url: string;
  fallback_source?: string;
  description?: string;
}

export interface PackageManifest {
  packages: Record<string, PackageConfig>;
  sources: Record<string, PackageSource>;
  remove: string[];
  settings: {
    auto_remove_unlisted: boolean;
    track_managed_packages: boolean;
    state_file: string;
    uv: {
      enabled: boolean;
      fallback_to_pip: boolean;
      version: string;
    };
  };
}

export interface PackageState {
  managed_packages: Record<string, {
    version: string;
    source: string;
    installed_at: string;
  }>;
  last_updated: string;
}

export interface InstallationPlan {
  install: Array<{ name: string; config: PackageConfig; spec: string; source: string }>;
  remove: string[];
  already_managed: string[];
}

export class BackendPackageManager {
  private manifest: PackageManifest;
  private pythonExePath: string;
  private sitePackagesPath: string;
  private stateFilePath: string;
  private uvPath: string | null = null;

  constructor(pythonExePath: string, sitePackagesPath: string) {
    this.pythonExePath = pythonExePath;
    this.sitePackagesPath = sitePackagesPath;
    
    // Load package manifest
    const manifestPath = this.getManifestPath();
    if (!fs.existsSync(manifestPath)) {
      throw new Error(`Package manifest not found: ${manifestPath}`);
    }
    
    this.manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    // Setup state file path
    this.stateFilePath = path.resolve(this.manifest.settings.state_file);
    
    // Ensure state directory exists
    const stateDir = path.dirname(this.stateFilePath);
    if (!fs.existsSync(stateDir)) {
      fs.mkdirSync(stateDir, { recursive: true });
    }
  }

  private getManifestPath(): string {
    // Look for backend_packages.json in the python directory
    const projectRoot = path.resolve(__dirname, '..');
    return path.join(projectRoot, 'python', 'backend_packages.json');
  }

  private loadState(): PackageState {
    if (!fs.existsSync(this.stateFilePath)) {
      return {
        managed_packages: {},
        last_updated: new Date().toISOString()
      };
    }
    
    try {
      return JSON.parse(fs.readFileSync(this.stateFilePath, 'utf8'));
    } catch (error) {
      console.warn(`Warning: Could not load package state, starting fresh: ${error}`);
      return {
        managed_packages: {},
        last_updated: new Date().toISOString()
      };
    }
  }

  private saveState(state: PackageState): void {
    state.last_updated = new Date().toISOString();
    fs.writeFileSync(this.stateFilePath, JSON.stringify(state, null, 2));
  }

  // Initialize UV for faster package management
  private async ensureUV(): Promise<string | null> {
    if (!this.manifest.settings.uv.enabled) {
      return null;
    }

    if (this.uvPath) {
      return this.uvPath; // Cached
    }

    try {
      const { downloadUV } = await import('./backend-utils');
      this.uvPath = await downloadUV();
      return this.uvPath;
    } catch (error) {
      console.warn('Failed to setup UV:', error);
      return null;
    }
  }

  // Get current platform for platform-specific packages
  private getCurrentPlatform(): string {
    return process.platform;
  }

  // Check if a package should be installed on current platform
  private shouldInstallPackage(packageConfig: PackageConfig): boolean {
    if (!packageConfig.platform) {
      return true; // No platform restriction
    }
    return packageConfig.platform === this.getCurrentPlatform();
  }

  // Build package specification string for installation
  private buildPackageSpec(packageName: string, packageConfig: PackageConfig): string {
    const source = this.manifest.sources[packageConfig.source];
    
    // For git sources, use the git URL directly
    if (source && source.type === 'git') {
      let spec = `git+${source.url}`;
      
      // Add extras if specified
      if (packageConfig.extras && packageConfig.extras.length > 0) {
        spec += `#egg=${packageName}[${packageConfig.extras.join(',')}]`;
      } else {
        spec += `#egg=${packageName}`;
      }
      
      return spec;
    }
    
    // For regular packages
    let spec = packageName;
    
    // Add extras if specified
    if (packageConfig.extras && packageConfig.extras.length > 0) {
      spec += `[${packageConfig.extras.join(',')}]`;
    }
    
    // Add version
    spec += packageConfig.version;
    
    return spec;
  }

  // Get installation arguments for a specific source
  private getSourceArgs(sourceName: string): string[] {
    const source = this.manifest.sources[sourceName];
    if (!source) {
      throw new Error(`Unknown package source: ${sourceName}`);
    }

    const args: string[] = [];
    
    if (source.type === 'index') {
      args.push('--index-url', source.url);
    } else if (source.type === 'git') {
      // Git sources don't need extra args, the URL is used directly in the spec
      return [];
    } else {
      throw new Error(`Unsupported source type: ${source.type}`);
    }
    
    return args;
  }

  // Get packages that need to be installed
  async getInstallationPlan(): Promise<InstallationPlan> {
    const state = this.loadState();
    const result: InstallationPlan = {
      install: [],
      remove: [...this.manifest.remove], // Copy remove list
      already_managed: []
    };

    // Check each package in manifest
    for (const [packageName, packageConfig] of Object.entries(this.manifest.packages)) {
      // Skip platform-specific packages that don't match current platform
      if (!this.shouldInstallPackage(packageConfig)) {
        continue;
      }

      const managedPackage = state.managed_packages[packageName];
      
      if (!managedPackage || 
          managedPackage.version !== packageConfig.version ||
          managedPackage.source !== packageConfig.source) {
        // Package needs to be installed/updated
        const spec = this.buildPackageSpec(packageName, packageConfig);
        result.install.push({
          name: packageName,
          config: packageConfig,
          spec: spec,
          source: packageConfig.source
        });
      } else {
        result.already_managed.push(packageName);
      }
    }

    // Check for packages to remove (if auto_remove_unlisted is enabled)
    if (this.manifest.settings.auto_remove_unlisted) {
      for (const managedPackage of Object.keys(state.managed_packages)) {
        if (!this.manifest.packages[managedPackage]) {
          result.remove.push(managedPackage);
        }
      }
    }

    return result;
  }

  // Install packages using optimized UV or pip fallback
  async installPackages(packagesToInstall: Array<{ name: string; config: PackageConfig; spec: string; source: string }>): Promise<void> {
    if (packagesToInstall.length === 0) {
      return;
    }

    // Initialize UV for fast installations
    const uvPath = await this.ensureUV();

    // Group packages by source for batch installation
    const packagesBySource = new Map<string, typeof packagesToInstall>();
    
    for (const pkg of packagesToInstall) {
      if (!packagesBySource.has(pkg.source)) {
        packagesBySource.set(pkg.source, []);
      }
      packagesBySource.get(pkg.source)!.push(pkg);
    }

    // Install packages grouped by source (UV can handle this efficiently)
    for (const [sourceName, packages] of packagesBySource) {
      await this.installPackageGroup(sourceName, packages, uvPath);
    }

    // Update state atomically
    const state = this.loadState();
    for (const pkg of packagesToInstall) {
      state.managed_packages[pkg.name] = {
        version: pkg.config.version,
        source: pkg.source,
        installed_at: new Date().toISOString()
      };
    }
    this.saveState(state);
  }

  // Install a group of packages from the same source
  private async installPackageGroup(
    sourceName: string, 
    packages: Array<{ name: string; config: PackageConfig; spec: string; source: string }>,
    uvPath: string | null
  ): Promise<void> {
    const packageSpecs = packages.map(pkg => pkg.spec);
    
    try {
      // Try UV first if available (much faster)
      if (uvPath) {
        await this.installWithUV(uvPath, packageSpecs, sourceName);
        return;
      }
    } catch (error) {
      if (!this.manifest.settings.uv.fallback_to_pip) {
        throw error;
      }
      console.warn(`UV installation failed for source ${sourceName}, falling back to pip:`, error);
    }

    // Fallback to pip
    await this.installWithPip(packageSpecs, sourceName);
  }

  // Install using UV with optimized settings
  private async installWithUV(uvPath: string, packageSpecs: string[], sourceName: string): Promise<void> {
    const args = [
      'pip', 'install',
      '--upgrade',
      '--no-cache', // UV handles caching more efficiently
      '--target', this.sitePackagesPath,
      ...this.getSourceArgs(sourceName),
      ...packageSpecs
    ];

    await runCommand(uvPath, args, {
      env: {
        ...process.env,
        PYTHONPATH: this.sitePackagesPath,
        UV_PYTHON: this.pythonExePath,
        // UV optimization environment variables
        UV_CONCURRENT_DOWNLOADS: '16', // Faster parallel downloads
        UV_NO_SYNC: '1' // Skip unnecessary sync operations
      }
    });
  }

  // Install using pip (fallback)
  private async installWithPip(packageSpecs: string[], sourceName: string): Promise<void> {
    const args = [
      '-m', 'pip', 'install',
      '--upgrade',
      '--no-cache-dir',
      '--target', this.sitePackagesPath,
      ...this.getSourceArgs(sourceName),
      ...packageSpecs
    ];

    await runCommand(this.pythonExePath, args, {
      env: {
        ...process.env,
        PYTHONPATH: this.sitePackagesPath
      }
    });
  }

  // Remove packages efficiently
  async removePackages(packagesToRemove: string[]): Promise<void> {
    if (packagesToRemove.length === 0) {
      return;
    }

    const uvPath = await this.ensureUV();

    try {
      if (uvPath) {
        await this.removeWithUV(uvPath, packagesToRemove);
      } else {
        await this.removeWithPip(packagesToRemove);
      }
    } catch (error) {
      console.warn(`Package removal failed for some packages: ${error}`);
      // Continue anyway, as removal failures are often not critical
    }

    // Update state atomically
    const state = this.loadState();
    for (const packageName of packagesToRemove) {
      delete state.managed_packages[packageName];
    }
    this.saveState(state);
  }

  // Remove using UV
  private async removeWithUV(uvPath: string, packageNames: string[]): Promise<void> {
    const args = [
      'pip', 'uninstall',
      '--yes',
      ...packageNames
    ];

    await runCommand(uvPath, args, {
      env: {
        ...process.env,
        PYTHONPATH: this.sitePackagesPath,
        UV_PYTHON: this.pythonExePath
      }
    });
  }

  // Remove using pip
  private async removeWithPip(packageNames: string[]): Promise<void> {
    const args = [
      '-m', 'pip', 'uninstall',
      '--yes',
      ...packageNames
    ];

    await runCommand(this.pythonExePath, args, {
      env: {
        ...process.env,
        PYTHONPATH: this.sitePackagesPath
      }
    });
  }

  // Sync packages to match manifest exactly
  async syncPackages(): Promise<{ installed: number; removed: number; unchanged: number }> {
    const plan = await this.getInstallationPlan();
    
    // Remove unwanted packages first
    if (plan.remove.length > 0) {
      await this.removePackages(plan.remove);
    }

    // Install/update needed packages
    if (plan.install.length > 0) {
      await this.installPackages(plan.install);
    }

    return {
      installed: plan.install.length,
      removed: plan.remove.length,
      unchanged: plan.already_managed.length
    };
  }

  // Get managed packages state
  getManagedPackages(): Record<string, { version: string; source: string; installed_at: string }> {
    const state = this.loadState();
    return state.managed_packages;
  }

  // Check if we're managing any packages
  hasManagedPackages(): boolean {
    const state = this.loadState();
    return Object.keys(state.managed_packages).length > 0;
  }

  // Get package categories for organized display
  getPackagesByCategory(): Record<string, string[]> {
    const categories: Record<string, string[]> = {};
    
    for (const [packageName, packageConfig] of Object.entries(this.manifest.packages)) {
      if (!this.shouldInstallPackage(packageConfig)) {
        continue;
      }
      
      const category = packageConfig.category || 'other';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(packageName);
    }
    
    return categories;
  }

  // Check if UV is available and working
  async isUVAvailable(): Promise<boolean> {
    const uvPath = await this.ensureUV();
    return uvPath !== null;
  }
} 